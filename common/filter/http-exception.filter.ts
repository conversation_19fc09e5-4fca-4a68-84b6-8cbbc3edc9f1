import {
  Catch,
  HttpException,
  ExceptionFilter,
  ArgumentsHost,
  BadRequestException,
} from '@nestjs/common';
import { Response, Request } from 'express';
//catch the exception name HttpException
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    let status = exception.getStatus();

    //this is the return format of the httpException
    if (
      exception.message ==
      'duplicate key value violates unique constraint "unique_index"'
    ) {
      exception.message = 'duplicate request_id';
    } else if (
      exception.message.search('admin_email') != -1 ||
      exception.message.search('duplicate_email') != -1
    ) {
      exception.message = 'Email had been taken';
    } else if (exception.message.search('duplicate_symbol') != -1) {
      exception.message = 'Symbol already support in system';
    } else if (exception.message.search('duplicate_name') != -1) {
      exception.message = 'Name had been taken';
    } else if (exception.message.search('duplicate_currency') != -1) {
      exception.message = 'Currency already support in system';
    } else if (exception.message.search('Too Many Requests') != -1) {
      exception.message = 'Too Many Requests , Only send email every 5 minutes';
    } else if (exception.message.search('Forbidden resource') != -1) {
      exception.message = 'Invalid permission';
      status = 400;
    }
    //this is the return format of the httpException
    return response.status(status).json({
      status: 'fail',
      message: exception.message,
    });
  }
}

@Catch()
export class ValidationExceptionFilter implements ExceptionFilter {
  public catch(exception, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    return (
      response
        .status(status)
        // you can manipulate the response here
        .json({
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: request.url,
        })
    );
  }
}
