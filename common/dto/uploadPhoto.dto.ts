import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsEnum,
  Min,
} from 'class-validator';

export class ValidatePhotoUploadDto {
    @ApiProperty({ description: 'property id' })
        @IsString()
        property_id: string;

    @ApiProperty({ description: 'photos to delete' })
        @IsString()
        photos_to_delete: string;
        
    @ApiProperty({ description: 'name of the photo' })
        @IsNotEmpty()
        @IsString()
        photo_name: string;
}
