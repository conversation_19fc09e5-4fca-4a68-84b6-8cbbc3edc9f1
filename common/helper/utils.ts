import { HttpException } from '@nestjs/common';
import configuration from 'config/configuration';
import * as crypto from 'crypto';
import * as base32 from 'hi-base32';
import { CustomErrorMessages } from './errorMessage';

export async function generateKeyPairFn(): Promise<{
  privateKey: string;
  publicKey: string;
}> {
  return new Promise((resolve, reject) => {
    crypto.generateKeyPair(
      'rsa',
      {
        modulusLength: 2048,
        publicExponent: 0x10001,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      },
      (err, publicKey, privateKey) => {
        if (err) {
          console.error('Error:', err);
          reject(err);
          return;
        }
        resolve({ privateKey, publicKey });
      },
    );
  });
}

// TODO DELETE THIS FUNCTION
export async function convertPublicKeyPemToDer(
  publicKeyPem: string,
): Promise<string> {
  const publicKey = crypto.createPublicKey({
    key: publicKeyPem,
    format: 'pem',
    type: 'spki',
  });

  const derBuffer = publicKey.export({
    format: 'der',
    type: 'spki',
  });

  return derBuffer.toString('base64');
}

// TODO DELETE THIS FUNCTION
export async function convertPublicKeyDerToPem(
  publicKeyDer: string,
): Promise<string> {
  //convert base64 DER back to buffer
  const derBuffer = Buffer.from(publicKeyDer, 'base64');

  const publicKey = crypto.createPublicKey({
    key: derBuffer,
    format: 'der',
    type: 'spki',
  });

  return publicKey.export({
    format: 'pem',
    type: 'spki',
  }) as string;
}

export async function encrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.secret;
    crypto.scrypt(password, 'salt', 16, (err, key) => {
      if (err) throw err;
      // Then, we'll generate a random initialization vector
      const iv = Buffer.alloc(16, random); // Initialization vector.
      if (err) throw err;
      const cipher = crypto.createCipheriv(algorithm, key, iv);

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      resolve(encrypted);
    });
  });
}

export async function bidnowEncrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = random;
    const key = crypto.pbkdf2(
      password,
      'salt',
      1000,
      16,
      'sha256',
      (err, key) => {
        if (err) throw err;
        // Then, we'll generate a random initialization vector
        const iv = Buffer.alloc(16, random); // Initialization vector.
        if (err) throw err;
        const cipher = crypto.createCipheriv(algorithm, key, iv);

        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        resolve(encrypted);
      },
    );
  });
}

export async function bidnowDecrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = random;
    const key = crypto.pbkdf2(
      password,
      'salt',
      1000,
      16,
      'sha256',
      (err, key) => {
        // The IV is usually passed along with the ciphertext.
        const iv = Buffer.alloc(16, random); // Initialization vector.

        const decipher = crypto.createDecipheriv(algorithm, key, iv);

        // Encrypted using same algorithm, key and iv.
        const encrypted = data;
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        resolve(decrypted);
      },
    );
  });
}

export async function decrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.secret;
    const key = crypto.scrypt(password, 'salt', 16, (err, key) => {
      // The IV is usually passed along with the ciphertext.
      const iv = Buffer.alloc(16, random); // Initialization vector.

      const decipher = crypto.createDecipheriv(algorithm, key, iv);

      // Encrypted using same algorithm, key and iv.
      const encrypted = data;
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      resolve(decrypted);
    });
  });
}

export async function generateWalletAdd(length = 40) {
  const randomBuffer = crypto.randomBytes(length);
  let r = base32.encode(randomBuffer).replace(/=/g, '');
  r = '0x' + r;
  return r;
}

export async function generateSecret(length = 20) {
  const randomBuffer = crypto.randomBytes(length);
  let r = base32.encode(randomBuffer).replace(/=/g, '');
  return r;
}

export async function generateSecretWithTimestamp(length = 20) {
  const timestamp = Date.now();
  const randomBuffer = crypto.randomBytes(length);
  let r = base32.encode(randomBuffer).replace(/=/g, '');

  return `${timestamp}${r}`;
}

function generateHOTP(secret, counter) {
  const decodedSecret = base32.decode.asBytes(secret);
  const buffer = Buffer.alloc(8);
  for (let i = 0; i < 8; i++) {
    buffer[7 - i] = counter & 0xff;
    counter = counter >> 8;
  }

  // Step 1: Generate an HMAC-SHA-1 value
  const hmac = crypto.createHmac('sha1', Buffer.from(decodedSecret));
  hmac.update(buffer);
  const hmacResult = hmac.digest();

  // Step 2: Generate a 4-byte string (Dynamic Truncation)
  const code = dynamicTruncationFn(hmacResult);

  // Step 3: Compute an HOTP value
  return code % 10 ** 6;
}

function dynamicTruncationFn(hmacValue) {
  const offset = hmacValue[hmacValue.length - 1] & 0xf;

  return (
    ((hmacValue[offset] & 0x7f) << 24) |
    ((hmacValue[offset + 1] & 0xff) << 16) |
    ((hmacValue[offset + 2] & 0xff) << 8) |
    (hmacValue[offset + 3] & 0xff)
  );
}

export function generateTOTP(secret, window = 0) {
  const counter = Math.floor(Date.now() / 30000);
  return generateHOTP(secret, counter + window)
    .toString()
    .padStart(6, '0');
}

export function verify2FA(secret, OTP: string) {
  let systemOTP = generateTOTP(secret).toString().padStart(6, '0');
  if (systemOTP == OTP) {
    return true;
  }
  throw new HttpException(CustomErrorMessages.COMMON.INVALID_2FA, 400);
}

export function getRandomProfileImage() {
  const array = [10, 20, 30, 40, 50];

  // Get a random index between 0 and the length of the array
  const randomIndex = Math.floor(Math.random() * array.length);

  // Access the element at the random index
  return array[randomIndex];
}
