function parseUnitHelper(number: string) {

    if (number === '0') {
        return '0';
    }

    let unitBehindDecimal = number.split('.');
    let unit = unitBehindDecimal[1] ? unitBehindDecimal[1].length : 0;
    let value = unitBehindDecimal[0] ? unitBehindDecimal[0] : '0';
    let unitStr = unitBehindDecimal[1] ? unitBehindDecimal[1] : '';


    let unitToAdd = 18 - unit;
    if (unitToAdd > 0) {
        for (let i = 0; i < unitToAdd; i++) {
            unitStr += '0';
        }
    }
    let finalValue = value + '.' + unitStr;
    return finalValue;
}

export { parseUnitHelper }