import * as crypto from 'crypto';
import configuration from 'config/configuration';

async function verifySignature(
    payload,
    nonce,
    timestamp,
    givenSignature,
) {
    // First concatenate as strings
    const signatureData = nonce + timestamp + payload;
    const secretKey = configuration().qn_token;
    // Convert to bytes
    const signatureBytes = Buffer.from(signatureData);

    const computedSignature = crypto
        .createHmac('sha256', secretKey)
        .update(signatureBytes)
        .digest('hex');

    return crypto.timingSafeEqual(
        Buffer.from(computedSignature, 'hex'),
        Buffer.from(givenSignature, 'hex'),
    );
}

export {
    verifySignature,
}
