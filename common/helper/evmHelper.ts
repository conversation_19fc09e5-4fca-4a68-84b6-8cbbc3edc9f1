import {
  createPublicClient,
  createWalletClient,
  http,
} from 'viem';
import { privateKeyToAccount, signMessage as sm2 } from 'viem/accounts';
import { polygon, polygonAmoy } from 'viem/chains';
import { ethers } from 'ethers';
import { decrypted } from './utils';
import configuration from 'config/configuration';

async function publicClient(chain: string) {
  const customTestnet = {
    ...polygonAmoy,
    fees: {
    //   // Optional: Add a buffer for fee spikes
    baseFeeMultiplier: 1.5,
    },
    rpcUrls: {
      ...polygonAmoy.rpcUrls,
      default: {
        http: [configuration().rpc_endpoint], // Your custom RPC URL
      },
      // public: {
      //   http: ['https://polygon-amoy-bor-rpc.publicnode.com'], // Optional: public fallback
      // },
    },
  };

  let network = {
    POLYGON_AMOY: customTestnet,
    POLYGON: {
      ...polygon,
      fees: {
        baseFeeMultiplier: 1.5
      }
    }
  };

  return createPublicClient({
    // account,
    chain: network[chain],
    transport: http(configuration().rpc_endpoint)
  });
}

async function deriveFromKey() {
  let key = process.env.PRIVATE_KEY;
  let decrypt = '0x' + (await decrypted(key, process.env.KEY_HASH));
  return {
    privateKey: decrypt,
    address: ethers.utils.computeAddress(decrypt),
  };
}

async function getPrivateKeyAccount(chain) {
  let keys = await deriveFromKey();
  const account = privateKeyToAccount(keys.privateKey as `0x${string}`);

  const customTestnet = {
    ...polygonAmoy,
    rpcUrls: {
      ...polygonAmoy.rpcUrls,
      default: {
        http: [configuration().rpc_endpoint], // Your custom RPC URL
      },
      // public: {
      //   http: ['https://polygon-amoy-bor-rpc.publicnode.com'], // Optional: public fallback
      // },
    },
  };

  let network = {
    POLYGON_AMOY: customTestnet,
    POLYGON: polygon,
  };

  let client = await createWalletClient({
    account,
    chain: network[chain],
    transport: http(),
  });

  return {
    client: client,
    account: account,
  };
  // return account
}

export {
  deriveFromKey
};
