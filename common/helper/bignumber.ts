import { BigNumber } from 'ethers';
import { parseEther } from 'viem';

function scientificToBigNumber(sciStr) {
  console.log(sciStr);
  sciStr = sciStr.toString();
  const parts = sciStr.toLowerCase().split('e');
  let coefficient = parts[0];
  let exponent = parts[1] ? parseInt(parts[1], 10) : 0;

  // If coefficient contains a decimal point, we need to adjust the exponent
  const decimalIndex = coefficient.indexOf('.');
  if (decimalIndex !== -1) {
    exponent -= coefficient.length - decimalIndex - 1;
    coefficient = coefficient.replace('.', '');
  }

  if (exponent >= 0) {
    coefficient = coefficient.padEnd(coefficient.length + exponent, '0');
  } else {
    coefficient = coefficient.padStart(coefficient.length - exponent, '0');
    coefficient =
      coefficient.slice(0, -exponent) + '.' + coefficient.slice(-exponent);
  }

  return BigNumber.from(coefficient);
}

function scientificToString(sciStr) {
  sciStr = sciStr.toString();
  const parts = sciStr.toLowerCase().split('e');
  let coefficient = parts[0];
  let exponent = parts[1] ? parseInt(parts[1], 10) : 0;

  const decimalIndex = coefficient.indexOf('.');
  if (decimalIndex !== -1) {
    coefficient = coefficient.replace('.', '');
    exponent -= (coefficient.length - decimalIndex);
  }

  let value;

  if (exponent >= 0) {

    value =  coefficient.padEnd(coefficient.length + exponent, '0');
  } else {
    const absExponent = Math.abs(exponent);

    if (absExponent >= coefficient.length) {
      value = '0.' + '0'.repeat(absExponent - coefficient.length) + coefficient;
    } else {
      value = coefficient.slice(0, coefficient.length - absExponent) + 
             '.' + 
             coefficient.slice(coefficient.length - absExponent);
    }
  }
  return value
}

function stringToBigInt(numStr, decimals = 18) {
  decimals = Math.min(18, Math.max(0, decimals));
  
  numStr = numStr.replace(/,|\s/g, '');
  
  if (!/^-?\d*\.?\d*$/.test(numStr)) {
    throw new Error('Invalid number format');
  }
  
  const [integerPart, decimalPart = ''] = numStr.split('.');
  
  // Handle negative sign separately
  const isNegative = integerPart.startsWith('-');
  const absIntegerPart = isNegative ? integerPart.substring(1) : integerPart;
  
  // Pad or truncate the decimal part to the specified number of decimals
  const paddedDecimalPart = decimalPart.padEnd(decimals, '0').slice(0, decimals);
  
  // Combine parts to create a string without the decimal point
  const combinedStr = absIntegerPart + paddedDecimalPart;
  
  // Remove leading zeros to avoid interpreting as octal
  const cleanedStr = combinedStr.replace(/^0+/, '') || '0';
  
  // Create the BigInt value
  let result = BigInt(cleanedStr);
  
  // Apply negative sign if necessary
  if (isNegative) {
    result = -result;
  }
  
  return {
    value: result,
    decimals: decimals,
    originalString: numStr,
    // Helper method to convert back to a decimal string
    toString: function() {
      const valueStr = this.value.toString();
      const isNeg = valueStr.startsWith('-');
      const absValueStr = isNeg ? valueStr.substring(1) : valueStr;
      
      // If decimals is 0, just return the value
      if (this.decimals === 0) {
        return valueStr;
      }
      
      // Pad with leading zeros if needed
      const paddedStr = absValueStr.padStart(this.decimals + 1, '0');
      
      // Insert decimal point at the right position
      const insertAt = paddedStr.length - this.decimals;
      const intPart = paddedStr.substring(0, insertAt) || '0';
      const decPart = paddedStr.substring(insertAt);
      
      // Construct the final string
      return (isNeg ? '-' : '') + intPart + (decPart !== '0' ? '.' + decPart : '');
    }
  };
}

function numberToBigint(amount: number, decimal: number) {
  try {
    if (decimal < 11) {
      return BigInt(Math.floor(amount * 10 ** decimal));
    } else {
      let big = scientificToBigNumber(Math.floor(amount * 10 ** 8));
      return big.mul(BigNumber.from(10 ** (decimal - 8))).toBigInt();
    }
  } catch (error) {
    console.log(error);
  }
}

function bigAdd(
  big1: BigInt,
  big2: BigInt,
  big3: BigInt = BigInt(0),
  big4: BigInt = BigInt(0),
) {
  let big = BigNumber.from(big1);
  return big
    .add(BigNumber.from(big2))
    .add(BigNumber.from(big3))
    .add(BigNumber.from(big4))
    .toBigInt();
}

function bigMinus(
  big1: BigInt,
  big2: BigInt,
  big3: BigInt = BigInt(0),
  big4: BigInt = BigInt(0),
  big5: BigInt = BigInt(0),
  big6: BigInt = BigInt(0),
) {
  let big = BigNumber.from(big1);
  return big
    .sub(BigNumber.from(big2))
    .sub(BigNumber.from(big3))
    .sub(BigNumber.from(big4))
    .sub(BigNumber.from(big5))
    .sub(BigNumber.from(big6))
    .toBigInt();
}

function bigMultiply(big1: BigInt, big2: BigInt, big3: BigInt = BigInt(1)) {
  let big = BigNumber.from(big1);
  return big.mul(BigNumber.from(big2)).mul(BigNumber.from(big3)).toBigInt();
}

function bigDivide(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.div(BigNumber.from(big2)).toBigInt();
}

function bigGreater(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.gt(BigNumber.from(big2));
}

function bigEqual(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.eq(BigNumber.from(big2));
}

function bigGreaterEqual(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.gte(BigNumber.from(big2));
}

function bigLesser(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.lt(BigNumber.from(big2));
}

function bigLesserEqual(big1: BigInt, big2: BigInt) {
  let big = BigNumber.from(big1);
  return big.lte(BigNumber.from(big2));
}

export {
  scientificToBigNumber,
  scientificToString,
  stringToBigInt,
  numberToBigint,
  bigAdd,
  bigMinus,
  bigMultiply,
  bigDivide,
  bigGreater,
  bigEqual,
  bigGreaterEqual,
  bigLesser,
  bigLesserEqual
};
