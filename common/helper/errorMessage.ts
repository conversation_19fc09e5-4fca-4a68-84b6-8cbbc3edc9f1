export const CustomErrorMessages = {
  AUTH: {
    INVALID_CREDENTIAL: 'Invalid credential',
    INVALID_PASSWORD: 'Invalid password',
    RESET_PASSWORD: 'Please reset password to login',
    ACCOUNT_NOT_FOUND: 'Account not exists',
    EMAIL_NOT_VERIFY: 'Email not verify',
    NOT_LOGIN: 'Must be login before perform any action',
    TOKEN_NOT_FOUND: 'Please provide valid token',
    INVALID_TOKEN: 'Invalid token',
    TOKEN_EXPIRED: 'Token is expired',
    INVALID_USER: 'User is not valid',
    INVALID_ADMIN: 'Admin is not valid',
    INVALID_TOKEN_ISSUER: 'Token issuer is not valid',
    BEARER_NOT_FOUND: 'Authorization header is missing',
    INVALID_BEARER: 'Invalid authorization header format',
    PAYLOAD_MISSING: 'Authentication payload is missing',
    EXPIRED_MISSING: 'Authentication payload expired time is missing',
    INVALID_TOKEN_PAYLOAD: 'Invalid token payload',
    PUBL<PERSON>_KEY_MISSING:
      "'bidnow_public_key' not found, please contact developer.",
  },
  NFT: {
    NFT_NOT_FOUND: 'nft not found',
    INVALID_ICO_DATE: 'Please enter a date that is later than today.',
    INVALID_LAUNCH_DATE:
      "Please ensure both NFT expired date and Prelaunch end date are larger than today's date.",
    INVALID_EXPIRE_DATE:
      'Please enter a expired date that is later than today and Prelaunch date.',
    ICO_ENDED:
      'Current time exceeds Prelaunch end date, unable to launch to marketplace',
    INVALID_APR: 'Please ensure the percentage is greater than 0.',
    INVALID_MAX_APR: 'Please ensure the max apr is greater than min apr.',
    INVALID_CAP_GAIN: 'Please ensure the percentage is greater than 0.',
    INVALID_MAX_CAP_GAIN: 'Please ensure the max cap gain is greater than min cap gain.',
    INVALID_UPDATE_NFT:
      "Please only include 'ico_price' params when 'is_onchain' is True.",
    NFT_EXPIRED: 'Nft has expired, unable to launch',
    NFT_REMAIN: 'Nft remaining supply larger than 0, unable to launch',
    NFT_LAUNCHED: 'Nft has launched, unable to delete',
    NFT_NOT_YET_LAUNCHED: 'Nft has not been launched yet',
    NFT_IS_DROP: 'Nft not found or have been release',
    INVALID_STATUS: 'Invalid Nft status to execute action',
    INSUFFICIENT_SUPPLY: 'Not enough remaining supply',
    INVALID_ICO_PRICE: 'Prelaunch price must be larger than 0',
    PINATA: 'Unable to upload photo to Pinata',
    INSUFFICIENT_NFT_BAL: 'Insufficient NFT balance.',
    INSUFFICIENT_CONTRACT_NFT_BAL: 'Insufficient NFT balance in the contract.',
    FAIL_SET_NFT_HOLDER: 'Failed to set custodial wallet (NFT Holder)',
    INVALID_NFT_HOLDER:
      'NFT Holder Address cannot be the same address as previous',
    FAIL_CREATE_NFT: 'Failed to create NFT',
    FAIL_UPDATE_NFT: 'Failed to update NFT',
    FAIL_LAUNCH_ICO: 'Failed to launch "Prelaunch',
    FAIL_AIRDROP_NFT: 'Failed to airdrop NFT',
    FAIL_LAUNCH_MARKETPLACE: 'Failed to launch NFT to Marketplace',
    FAIL_BUY_ICO: 'Failed to buy Prelaunch',
    FAIL_UPDATE_ICO_PRICE: 'Failed to update Prelaunch price',
    FAIL_INSERT_BASE_URI: 'Failed to insert baseURI (IPFS)',
    FAIL_SWAP_NFT_TO_TOKEN: 'Failed to swap NFT to BidNow token',
    FAIL_WITHDRAW_NFT_FROM_CONTRACT:
      'Failed to withdraw NFT from marketplace contract',
    FAIL_DEPOSIT_BIDNOW_TANK_BAL: 'Failed to deposit BidNow tank balance',
    FAIL_TO_GET_BIDNOW_TANK_BAL: 'Failed to get BidNow tank balance',
    FAIL_TO_PURCHASE_REMAINING: 'Failed to purchase remaining nft',
    FAIL_TO_GET_NFT_ALLOWANCE: 'Failed to get NFT allowance',
    FAIL_APPROVE_NFT_ALLOWANCE: 'Failed to approve NFT allowance',
    FAIL_TO_GET_NATIVE_TOKEN_BALANCE: 'Failed to get native token balance.',
    INSUFFICIENT_AIRDROP_WALLET_BALANCE:
      'Insufficient admin_to_airdrop wallet balance.',
    INVALID_AIRDROP_DATA: 'Input contains invalid airdrop data.',
    FAIL_GET_NFT_BAL: 'Fail to get NFT balance.',
    ADMIN_FAIL_TO_AIRDROP_NFT: 'Fail to airdrop NFT.',
    FAIL_UPDATE_EXPIRED_TIME: 'Fail to update NFT expired time.',
    INVALID_EXPIRED_DATE: 'Invalid expired date, please ensure it is later than today.',
    RETURN_HASH_ARR_LENGTH_UNMATCHED: 'Return hash array length unmatched.',
    FAIL_WITHDRAW_BIDNOW_TANK_BAL: 'Failed to withdraw BidNow tank balance from contract.',
    INSUFFICIENT_BIDNOW_TANK_BAL: 'Insufficient BidNow tank balance.',
  },
  PROPERTY: {
    PROPERTY_NOT_FOUND: 'property not found',
    INVALID_PROPERTY: 'Invalid property id',
    INVALID_JSON_INPUT: 'Please insert a valid JSON format.',
    IMAGE_COUNT:
      'Please ensure upload a minimum of 1 image and no more than 5 images.',
    INVALID_NEW_IMG_COUNT: 'Not enough positions available for new images',
    INVALID_IMAGE_URL: 'Invalid existing photo URL',
    INVALID_QUERY_STATUS:
      "Please leave nft_status as 'empty' if search for drafted property",
    NFT_BINDED: (property_id) =>
      `An NFT has already been assigned to the property with ID ${property_id}`,
  },
  COMMON: {
    INVALID_INPUT: 'Input contain invalid entry. Please retry.',
    DAVID: 'Something is wrong, please contact developer',
    INVALID_KEY: 'Invalid old public key, regenerate denied.',
    MISSING_KEY: 'Please input the old public key to verify',
    MISSING_SIGNATURE: 'Signature not provided',
    INVALID_SIGNATURE: 'Invalid Signature',
    INVALID_2FA: 'Invalid 2FA',
    MISSING_SETUP:
      "System keypair not found, please generate through '/generate-public-key' route.",
  },
  USER: {
    INVALID_NONCE:
      'The nonce must be within 1 minutes of the current timestamp.',
  },
  MORALIS: {
    UNABLE_VERIFY: 'Unable to verify message',
    INVALID_ADDRESS: 'Invalid address',
    FAIL_ICO: 'Failed to create Prelaunch',
    FAIL_SET_MARKET: `Failed to Set Market Status`,
    FAIL_SET_PRICE: `Failed to Set Price`,
    FAIL_WHITELIST: `Failed to Whitelist User`,
    FAIL_SET_FEE: `Failed to Set Either Penalty, Buy or Sell Fee`,
    FAIL_CHANGE_MARKET_STATUS: `Failed to Change Marketplace Status`,
    FAIL_SET_NFT_HOLDER: `Failed to Set NFT Holder`,
    FAIL_GET_ICO_DETAIL: `Failed to Get Prelaunch Detail.`,
    FAIL_BUY_ICO: `Failed to Buy Prelaunch`,
    FAIL_SET_URI: `Failed to set NFT`,
    LISTING_ID_NOT_FOUND: 'Unable to get listing id.',
    LISTING_DETAIL_NOT_FOUND: 'Unable to get listing detail.',
    FAIL_PLACE_ORDER: 'Fail to place order.',
    FAIL_DELIST_ORDER: 'Fail to delist order.',
    BUY_SELL_ORDER_BOOK_NOT_FOUND: 'Fail to retrieve buy & sell order book.',
    ORDER_COUNT_NOT_FOUND: 'Order count not found.',
    FAIL_GET_FEE: 'Fail to get fee.',
    FAIL_GET_AVAILABLE_WITHDRAW: 'Fail to get available withdraw.',
    FAIL_SWAPBACK_BIDNOW: 'Fail to swap back BidNow token.',
    FAIL_WITHDRAW_NFT: 'Fail to withdraw NFT.',
    FAIL_GET_TANK_BAL: "Fail to get NFT's BidNow token tank balance.",
    FAIL_DEPOSIT_TANK_BAL: "Fail to deposit NFT's BidNow token tank balance.",
    FAIL_WITHDRAW_FEE: 'Fail to withdraw fee from contract.',
    FAIL_WITHDRAW_GAS_FEE: 'Fail to withdraw gas fee from contract',
    FAIL_SET_ORDERBOOK_LIMIT: 'Failed to set orderbook limit',
    FAIL_GET_ORDERBOOK_LIMIT: 'Failed to get orderbook limit',
    FAIL_RECORD_LAUNCH_ICO_CALLBACK: 'Failed to record launch Prelaunch callback',
    FAIL_RECORD_ICO_BUY_CALLBACK: 'Failed to record Prelaunch buy callback',
    FAIL_RECORD_LAUNCH_MARKETPLACE_CALLBACK:
      'Failed to record launch marketplace callback',
    FAIL_RECORD_AIRDROP_NFT_CALLBACK: 'Failed to record airdrop NFT callback',
    FAIL_RECORD_UPDATE_ICO_PRICE_CALLBACK:
      'Failed to record update Prelaunch price callback',
    FAIL_RECORD_ADMIN_WITHDRAW_NFT_CALLBACK:
      'Failed to record admin withdraw NFT callback',
    FAIL_RECORD_ADMIN_WITHDRAW_FEE_CALLBACK:
      'Failed to record admin withdraw fee callback',
    FAIL_RECORD_ADMIN_TOPUP_BIDNOW_CALLBACK:
      'Failed to record admin deposit tank balance callback',
    FAIL_RECORD_ADMIN_SET_FEE_CALLBACK:
      'Failed to record admin set fee callback',
    FAIL_RECORD_SET_CUSTODIAL_WALLET_CALLBACK:
      'Failed to record set custodial wallet callback',
    FAIL_TO_GET_DEV_PERCENTAGE: 'Failed to get dev percentage',
    FAIL_TO_SET_DEV_PERCENTAGE: 'Fail to set dev percentage.',
    FAIL_TO_GET_GAS_FEE: 'Fail to get gas fee.',
    FAIL_TO_WITHDRAW_GAS_FEE: 'Fail to withdraw gas fee.',
    FAIL_WITHDRAW_TANK_BAL: 'Fail to withdraw tank balance.',
  },
  ORDER_BOOK: {
    LOT_NOT_FOUND: "Unable to find user's position.",
    INSUFFICIENT_LOT: 'Insufficient lot / balance.',
    LISTING_DETAIL_NOT_FOUND: 'Unable to find listing detail.',
    FEE_OUT_OF_RANGE: 'Percentage value must be between 0% and 50%',
    INVALID_GAS_FEE: 'Gas fee amount must be over 0 BID',
    FAIL_CREATE_ORDER: 'Failed to create order',
    FAIL_REMOVE_ORDER: 'Failed to remove order',
    FAIL_RECORD_SELL_ORDER_CALLBACK: 'Failed to record sell order callback',
    FAIL_RECORD_BUY_ORDER_CALLBACK: 'Failed to record buy order callback',
    FAIL_RECORD_ORDER_MATCH_CALLBACK: 'Failed to record order match callback',
    FAIL_RECORD_ORDER_REMOVE_CALLBACK: 'Failed to record order remove callback',
  },
  EVENT: {
    INVALID_EVENT_DATE:
      "Please ensure Event date are larger than today's date.",
  },
  BIDNOW: {
    INSUFFICIENT_BIDNOW_BAL: 'Insufficient Bidnow Balance.',
    FAIL_GET_BIDNOW_BAL: 'Fail to get Bidnow Balance.',
    SESSION_NOT_FOUND: 'Session not found',
    SESSION_EXPIRED: 'Session expired',
    INVALID_4337: 'Invalid 4337 wallet address',
    USER_NOT_FOUND: 'No user found in system',
    ADDRESS_DUPLICATE:
      'The bidnow return address is existed in marketplace system,please use a new address',
    TRANSACTION_NOT_FOUND: 'No transaction found in system',
    MINIMUM_AMOUNT: 'Amount must be greater than 0',
    MISSING_RECEIVER:
      'Receiver is null ,Please set receiver address using updateReturnAddress API before proceed',
    NOT_AUTH: 'Session not authenticated',
    REF_NOT_UNIQUE: 'Ref ID is not unique',
    WEBHOOK_EXISTED: 'Webhook is existed in same type',
    FAIL_GET_BID_TOKEN_ALLOWANCE: 'Fail to get bid token allowance',
    FAIL_APPROVE_BID_ALLOWANCE: 'Fail to approve bid token allowance',
    INSUFFICIENT_BUNDLER_PAYMASTER_BALANCE: 'Insufficient Bundler / Paymaster balance, please contact admin.',
    AMOUNT_DECIMAL_PLACES: 'Amount must be a number with 18 decimal places',
    MARKETPLACE_FREEZED: 'Marketplace for this NFT has been disabled.',
  },
  IMAGE: {
    MAX_SIZE: (size) => `File size too large. Maximum size is ${size}MB`,
    FILE_TYPE: (type) => `Invalid file type. Allowed types are: ${type}`,
    MAX_FILE: (file) => `Too many files. Maximum ${file} files allowed`,
  },
  ADMIN: {
    ADMIN_BALANCE: 'Insufficient admin wallet balance, please top-up Polygon in order to proceed.',
    NFT_KEEPER_BALANCE: 'Insufficient "NFT Keeper" wallet balance, please top-up Polygon in order to proceed.',
    ADMIN_FOR_AIRDROP_BALANCE: 'Insufficient "Admin for Airdrop" wallet balance, please top-up Polygon in order to proceed.',
    PERCENTAGE_OUT_OF_RANGE: 'Input percentage out of range.',
    FAIL_TO_GET_FEE_HISTORY: 'Fail to get fee history.',
    FAIL_TO_RETRIVE_PCT_DATA:
      'Fail to retrive old percentage data to calculate distribution (No record inside database). Unable to proceed, please contact developer.',
    FAIL_TO_GET_BID_TOKEN_PRICE: 'Fail to fetch Bid token price.',
    FAIL_TO_GET_ALL_SETTINGS_CONSTANT: 'Fail to get all settings constant.',
    FAIL_TO_GET_FEE_COLLECTOR_ADDRESS: 'Fail to get fee collector addresses.',
    INCORRECT_FEE_COLLECTOR_ADDRESS_NAME: 'Incorect fee address name.',
    FAIL_TO_UPDATE_FEE: 'Fail to update fee setting, please contact admin.',
    FX_RATE_OUT_OF_RANGE: 'FX rate out of range.',
    ADMIN_ERROR_MESSAGE: 'An error occurred while performing the action. This may be due to insufficient Polygon in the admin wallet. Please check the wallet balance and top it up if necessary. If the issue persists after retrying, contact the developer for further assistance.',
  },
};
