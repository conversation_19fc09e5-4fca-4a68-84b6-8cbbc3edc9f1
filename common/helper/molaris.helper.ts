import configuration from 'config/configuration';
import axios from 'axios';
import { keccak256, stringToBytes } from 'viem';
import { CustomErrorMessages } from './errorMessage';
async function rescan(bn: string, network: string) {
  let moralis_setting = {
    BSC_TESTNET: {
      hex: '0x61',
      stream_id: '/2353f37b-a0fb-45bd-b312-bf340c264534',
    },
    SEPOLIA: {
      hex: '0xaa36a7',
      stream_id: '/2353f37b-a0fb-45bd-b312-bf340c264534',
    },
  };
  let url =
    'https://api.moralis-streams.com/streams/evm/' +
    moralis_setting[network].hex +
    '/block-to-webhook/' +
    bn.toString() +
    moralis_setting[network].stream_id;
  const headers = {
    accept: 'application/json',
    'X-API-Key': configuration().moralis.moralis_key,
  };

  await axios.post(url, {}, { headers });
}

function filterAndMatchTransactions(logs, txns, topics) {
  const filteredLogs = logs.filter((log) => topics.includes(log.topic0));

  const result = [];
  filteredLogs.forEach((log) => {
    const txnHash = log.transactionHash;
    const txs = txns.find((tx) => tx.hash === txnHash);
    if (txs) {
      result.push({
        log,
        txs,
      });
    }
  });
  return result;
}

function validateMoralisRequest(request: any) {
  const providedSignature = request.headers['x-signature'];
  if (!providedSignature) {
    throw new Error(CustomErrorMessages.COMMON.MISSING_SIGNATURE);
  }
  const data = stringToBytes(
    JSON.stringify(request.body) + configuration().moralis.moralis_secret,
  );
  const generatedSignature = keccak256(data);
  if (generatedSignature !== providedSignature) {
    throw new Error(CustomErrorMessages.COMMON.INVALID_SIGNATURE);
  }
}

function formatEthereumAddress(address: string): string {
  // Remove the '0x' prefix
  const cleanAddress = address.replace(/^0x/, '');
  
  // Remove up to 24 leading zeros from the address
  const trimmedAddress = cleanAddress.replace(/^0{0,24}/, '');
  
  // Convert to checksum address (mixed case)
  return `0x${trimmedAddress}`;
}

export { rescan, filterAndMatchTransactions, validateMoralisRequest, formatEthereumAddress };
