import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

export function ExtendsSessionMiddleware(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  let COOKIE_MAX_AGE = 30 * 60 * 1000;

  if (req.headers.cookie && req.headers.cookie.search('bn-ad=') != -1) {
    let token_1 = req.headers.cookie.split(' ');
    let token = token_1
      .find((item) => item.search('bn-ad=') != -1)
      .split('bn-ad=')[1];
    if (token[token.length - 1] == ';') {
      token = token.slice(0, -1);
    }
    const expirationDate = new Date(Date.now() + COOKIE_MAX_AGE);
    res.cookie('bn-ad', token, {
      httpOnly: true, // Prevent access by JavaScript
      sameSite: 'none',
      secure: true,
      // expires: expirationDate,
      maxAge: COOKIE_MAX_AGE,
    });
  } else if (
    req.headers.cookie &&
    req.headers.cookie.search('bn-user=') != -1
  ) {
    let token_1 = req.headers.cookie.split(' ');
    let token = token_1
      .find((item) => item.search('bn-user=') != -1)
      .split('bn-user=')[1];
    if (token[token.length - 1] == ';') {
      token = token.slice(0, -1);
    }
    const expirationDate = new Date(Date.now() + COOKIE_MAX_AGE);
    res.cookie('bn-user', token, {
      httpOnly: true, // Prevent access by JavaScript
      sameSite: 'none',
      secure: true,
      // expires: expirationDate,
      maxAge: COOKIE_MAX_AGE * 48,
    });
  }
  next();
}
