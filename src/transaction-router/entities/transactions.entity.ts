import { isInt } from 'class-validator';
import { Entity, PrimaryColumn, Column, CreateDateColumn, Index } from 'typeorm';

export enum TransactionType {
  LEGACY = 'legacy',     // Pre-EIP-1559 transactions
  EIP_2930 = 'eip2930',  // Access list transactions  
  EIP_1559 = 'eip1559',  // Type 2 transactions (most common now)
}

export enum TransactionStatus {
  SUCCESS = 'success',   // Transaction succeeded 
  FAILED = 'failed',     // Transaction failed (reverted)   
  PENDING = 'pending',   // Not yet mined 
}

@Entity('transactions')
@Index(['from'])
@Index(['to'])
@Index(['networkId', 'blockNumber'])
export class Transaction {
  @PrimaryColumn({ type: 'varchar'})
  hash: string;

  @Column({ type: 'int' })
  networkId: number;

  @Column({ type: 'varchar' })
  from: string;

  @Column({ type: 'varchar'})
  to: string;

  @Column({ type: 'varchar', })
  value: string;

  @Column({ type: 'varchar'})
  blockNumber: string;

  @Column({ type: 'varchar'})
  blockTimestamp: string;

  @Column({ type: 'varchar'})
  gasPrice: string;   

  @Column({ type: 'varchar', nullable: true })
  gasUsed: string;

  @Column({ type: 'varchar', })
  transactionType: TransactionType;

  @Column({ type: 'varchar'})
  transactionIndex: string;

  @Column({ type: 'varchar'})
  nonce: string;
  
  @Column({ type: 'varchar', length: 10 })
  chainId: string; // "0x1" for Ethereum

  @Column({ type: 'varchar', nullable: true })
  status?: TransactionStatus;

  @CreateDateColumn()
  created_at: Date;
}