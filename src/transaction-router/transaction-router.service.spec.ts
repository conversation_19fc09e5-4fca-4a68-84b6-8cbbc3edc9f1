import { Test, TestingModule } from '@nestjs/testing';
import { TransactionRouterService } from './transaction-router.service';

describe('TransactionRouterService', () => {
  let service: TransactionRouterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TransactionRouterService],
    }).compile();

    service = module.get<TransactionRouterService>(TransactionRouterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
