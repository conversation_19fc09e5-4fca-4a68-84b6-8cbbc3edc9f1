import { Test, TestingModule } from '@nestjs/testing';
import { TransactionRouterController } from './transaction-router.controller';
import { TransactionRouterService } from './transaction-router.service';

describe('TransactionRouterController', () => {
  let controller: TransactionRouterController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TransactionRouterController],
      providers: [TransactionRouterService],
    }).compile();

    controller = module.get<TransactionRouterController>(TransactionRouterController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
