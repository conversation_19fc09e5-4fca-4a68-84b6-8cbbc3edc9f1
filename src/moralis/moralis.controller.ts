import { <PERSON>, <PERSON>, Req } from '@nestjs/common';
import { MoralisService } from './moralis.service';
import { ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';

@Controller('moralis')
@ApiTags('Moralis')
export class MoralisController {
  constructor(private readonly moralisService: MoralisService) {}

  @Post('add')
  async addStream() {
    await this.moralisService.createStream(
      ['0x4e3a374D914E182bbFBA2b8448CE6cE78230Bbc9'],
      'https://sapi.turbox.bond/bond/moralis/bond'
    )
  }

  @Post('delete')
  async deleteStream() {
    await this.moralisService.deleteStream('587624d9-14fe-48c7-a8a9-043415236d56')
  }

  @Post('add-address')
  async addAddress() {
    await this.moralisService.addAddressToStream(
      '3059ad93-fea0-4610-86e3-899b9ea35750',
      ['0x8E946e9a3E4Cc49bd28CEf59e15CD4F084f3bF29']
    )
  }

  @Post('moralis')
  @ApiExcludeEndpoint()
  async moralisCallback(@Req() request: any) {
    return await this.moralisService.moralisCallback(request);
  }
}
