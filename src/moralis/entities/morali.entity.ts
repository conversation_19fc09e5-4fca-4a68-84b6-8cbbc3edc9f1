import {
  Column,
	CreateDateColumn,
	En<PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from "typeorm";

@Entity()
export class Morali {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  hash: string;

	@Column({
		default: '0',
		nullable: true,
		type: 'decimal',
		precision: 40,
		scale: 0,
		transformer: {
			to: (value: bigint) => (value ? BigInt(value) : 0),
			from: (value: bigint) => (value ? value.toString() : 0),
		},
	})
	amount: string;

	@CreateDateColumn()
	created_at: string;

	@UpdateDateColumn()
	updated_at: string;
}
