import { Injectable, Logger } from '@nestjs/common';
import <PERSON><PERSON><PERSON> from 'moralis';
import  { Evm<PERSON>hain } from '@moralisweb3/common-evm-utils';
import { filterAndMatchTransactions } from 'common/helper/molaris.helper';
import configuration from 'config/configuration';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class MoralisService {
  private readonly logger = new Logger(MoralisService.name);
  private readonly MAX_ADDRESSES_PER_STREAM = 100;

  constructor(
    @InjectQueue('transfer')
    private transferQueue: Queue,
  ) {
    if (!Moralis.Core.isStarted) {
      Moralis.start({
        apiKey: process.env.MORALIS_API_KEY,
      });
    }
  }

  async createStream(addresses: string[], webhookUrl: string, description?: string) {
    try {
      const stream = await Moralis.Streams.add({
        webhookUrl,
        description: description || `Stream created at ${new Date().toISOString()}`,
        tag: "wallet-monitor",
        chains: ["0x61"],
        includeNativeTxs: true,
        includeContractLogs: true,
      });

      if (addresses.length > 0) {
        await Moralis.Streams.addAddress({
          id: stream.raw.id,
          address: addresses,
        });
      }

      this.logger.log(`Created new stream with ID: ${stream.raw.id}`);
      return stream;
    } catch (error) {
      this.logger.error('Error creating stream:', error);
      throw error;
    }
  }

  async getStreams() {
    try {
      const streams = await Moralis.Streams.getAll({
        limit: 100,
      });
      return streams;
    } catch (error) {
      this.logger.error('Error fetching streams:', error);
      throw error;
    }
  }

  async getStreamById(streamId: string) {
    try {
      const stream = await Moralis.Streams.getById({ id: streamId });
      return stream;
    } catch (error) {
      this.logger.error(`Error fetching stream ${streamId}:`, error);
      throw error;
    }
  }

  async getStreamAddresses(streamId: string) {
    try {
      const addresses = await Moralis.Streams.getAddresses({
        id: streamId,
      });
      return addresses;
    } catch (error) {
      this.logger.error(`Error fetching addresses for stream ${streamId}:`, error);
      throw error;
    }
  }

  async addAddressToStream(streamId: string, addresses: string[]) {
    try {
      const result = await Moralis.Streams.addAddress({
        id: streamId,
        address: addresses,
      });
      this.logger.log(`Added addresses to stream ${streamId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error adding addresses to stream ${streamId}:`, error);
      throw error;
    }
  }

  async deleteStream(streamId: string) {
    try {
      await Moralis.Streams.delete({ id: streamId });
      this.logger.log(`Deleted stream ${streamId}`);
    } catch (error) {
      this.logger.error(`Error deleting stream ${streamId}:`, error);
      throw error;
    }
  }

  async findAvailableStream(): Promise<{ streamId: string; currentAddresses: string[] } | null> {
    try {
      const streams = await this.getStreams();
      
      for (const stream of streams.raw.result) {
        try {
          const streamAddresses = await this.getStreamAddresses(stream.id);
          const addressCount = streamAddresses.raw.result?.length || 0;
          
          if (addressCount < this.MAX_ADDRESSES_PER_STREAM) {
            return {
              streamId: stream.id,
              currentAddresses: streamAddresses.raw.result?.map((addr: any) => addr.address) || [],
            };
          }
        } catch (addressError) {
          this.logger.warn(`Could not get addresses for stream ${stream.id}:`, addressError);
          continue;
        }
      }
      
      return null;
    } catch (error) {
      this.logger.error('Error finding available stream:', error);
      throw error;
    }
  }

  async moralisCallback(request) {
    try {
      const targetAddress = '0x8E946e9a3E4Cc49bd28CEf59e15CD4F084f3bF29';
      const formatTransactions = [];

      if (request.body.erc20Transfers && request.body.erc20Transfers.length > 0) {
        const tokenTransfers = request.body.erc20Transfers
          .filter(transfer => transfer.to.toLowerCase() === targetAddress.toLowerCase())
          .map(transfer => ({
            type: 'token-transfer',
            transactionHash: transfer.transactionHash,
            contract: transfer.contract,
            from: transfer.from,
            to: transfer.to,
            value: transfer.value,
            valueWithDecimals: transfer.valueWithDecimals,
            tokenSymbol: transfer.tokenSymbol,
            tokenName: transfer.tokenName,
          }));
        
        formatTransactions.push(...tokenTransfers);
      }

      if (request.body.txs && request.body.txs.length > 0) {
        const nativeTransfers = request.body.txs
          .filter(tx => 
            tx.toAddress && 
            tx.toAddress.toLowerCase() === targetAddress.toLowerCase() && 
            tx.value !== "0"
          )
          .map(tx => ({
            type: 'native-transfer',
            transactionHash: tx.hash,
            from: tx.fromAddress,
            to: tx.toAddress,
            value: tx.value,
            gasUsed: tx.receiptGasUsed,
            gasPrice: tx.gasPrice,
          }));
        
        formatTransactions.push(...nativeTransfers);
      }

      await this.transferQueue.add(
        'process-transfer',
        { type: 'token-transfer', transactions: formatTransactions},
        { removeOnComplete: true },
      );
    } catch (error) {
      console.log(
        `-----------------------------------------------------------------------`,
      );
      console.log(new Date());
      console.log(error, 'in moralis service');
      console.log(
        `-----------------------------------------------------------------------`,
      );
    }
  }
}