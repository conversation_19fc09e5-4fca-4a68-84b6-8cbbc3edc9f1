import { Module } from '@nestjs/common';
import { MoralisService } from './moralis.service';
import { MoralisController } from './moralis.controller';
import { BullModule } from '@nestjs/bull';
import configuration from 'config/configuration';

@Module({
  imports: [
    BullModule.registerQueue(
      {
        name: 'transfer',
        redis: {
          port: 6379,
        }
      }
    )
  ],
  controllers: [MoralisController],
  providers: [MoralisService],
})
export class MoralisModule {}
