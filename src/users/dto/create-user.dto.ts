import { ApiProperty } from "@nestjs/swagger";
import { IsE<PERSON>, IsNotEmpty, IsString, MinLength } from "class-validator";

export class CreateUserDto {
  @ApiProperty({ example: '<PERSON>'})
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: 'Acme Corp'})
  @IsNotEmpty()
  @IsString()
  company: string;

  @ApiProperty({ example: '<EMAIL>'})
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password'})
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;
}

