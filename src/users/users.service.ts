import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
  constructor(    
    @InjectEntityManager()
    private readonly entityManager: EntityManager,){}

  // create new user 
  async create(createUserDto: CreateUserDto){
    try {
      const existingUser = await this.entityManager.createQueryBuilder(User, 'user')
        .where('user.email = :email', { email: createUserDto.email })
        .getOne();

      if (existingUser) {
        throw new BadRequestException('Email already in use');
      }

      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(User)
        .values({
          name: createUserDto.name,
          company: createUserDto.company,
          email: createUserDto.email,
          password: hashedPassword,
        })
        .execute();

        return ({ message: 'User created successfully' });
    } catch (error){
      throw new BadRequestException("Failed to create user",error.message);
    }
  }

  // get all users 
  async findAll(){
    return this.entityManager
      .createQueryBuilder(User, 'user')
      .select(['user.id', 'user.name', 'user.company', 'user.email', 'user.created_at', 'user.updated_at'])
      .orderBy('user.id', 'ASC')
      .getMany();
  }

  // get user by id 
  async findOne(id: number){
    return this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id })
      .getOne();
  }

  // update user by id 
  async update(id: number, updateUserDto: UpdateUserDto){
    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id })
      .getOne();

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.email = :email', { email: updateUserDto.email })
        .getOne();

      if (existingUser) {
        throw new BadRequestException('Email already in use');
      }
    }

    let hashedPassword = user.password;
    if (updateUserDto.password) {
      const saltRounds = 10;
      hashedPassword = await bcrypt.hash(updateUserDto.password, saltRounds);
    }

    await this.entityManager
      .createQueryBuilder()
      .update(User)
      .set({
        name: updateUserDto.name ?? user.name,
        company: updateUserDto.company ?? user.company,
        email: updateUserDto.email ?? user.email,
        password: hashedPassword,
      })
      .where('id = :id', { id })
      .execute();

    return { message: 'User updated successfully' };
  }

  // delete user by id 
  async remove(id: number){
    const result = await this.entityManager
      .createQueryBuilder()
      .delete()
      .from(User)
      .where('id = :id', { id })
      .execute();

    if (result.affected === 0) {
      throw new BadRequestException('User not found');
    }

    return { message: 'User deleted successfully' };
  }
}
