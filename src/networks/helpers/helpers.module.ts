import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EVMHelper } from './evm.helpers';
import { Network } from '../entities/network.entity';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    TypeOrmModule.forFeature([Network])
  ],
  providers: [EVMHelper],
  exports: [EVMHelper],
})
export class HelpersModule {}
