import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, IsEnum, IsObject } from 'class-validator';
import { NetworkType } from '../entities/network.entity';

export class CreateNetworkDto {
  @ApiProperty({ example: 'ethereum-mainnet'})
  @IsString()
  display_name: string;

  @ApiProperty({ example: 'Ethereum Mainnet'})
  @IsString()
  name: string;

  @ApiProperty({ example: 'ETH'})
  @IsString()
  symbol: string;

  @ApiProperty({ example: 1 })
  @IsNumber()
  chain_id: number;

  @ApiProperty({ example: 'EVM' })
  @IsEnum(NetworkType, { message: 'Type must be a valid NetworkType' })
  type: NetworkType;

  @ApiProperty({ example: 12 })
  @IsNumber()
  block_time_seconds: number;

  @ApiProperty({ example: { jsonrpc: "2.0", method: "eth_blockNumber", params: [], id: 1 }})
  @IsOptional()
  @IsObject()
  rpc_payload?: object;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  is_testnet?: boolean;
}