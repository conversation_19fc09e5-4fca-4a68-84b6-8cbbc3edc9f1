import { Injectable, BadRequestException } from '@nestjs/common';
import { EVMHelper } from './helpers/evm.helpers';
import { NetworkType } from './entities/network.entity';

@Injectable()
export class NetworkHelperService {
  constructor(
    private evmHelper: EVMHelper
    // private solanaHelper: SolanaHelper
  ) {}

  // routes to correct helper based on network type
  async getBlockNumber(network_name: string, networkType: NetworkType): Promise<any> {
    try {
      switch (networkType) {
        case NetworkType.EVM:
          return await this.evmHelper.getBlockNumber(network_name, networkType);
        
        // case NetworkType.SOLANA:
        //   return await this.solanaHelper.getBlockNumber(network_name, networkType);
        
        default:
          throw new BadRequestException(`Unsupported network type: ${networkType}`);
      }
    } catch (error) {
      throw new BadRequestException(
        `Failed to get block number for ${network_name} (${networkType}): ${error.message}`
      );
    }
  }

  // validate network support
  validateNetworkSupport(network_name: string, networkType: NetworkType): boolean {
    try {
      switch (networkType) {
        case NetworkType.EVM:
          return this.evmHelper.getSupportedNetworks().includes(network_name.toLowerCase());
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  // list all supported networks
  listAllSupportedNetworks(): Record<string, string[]> {
    return {
      [NetworkType.EVM]: this.evmHelper.getSupportedNetworks(),
      // Add other network types when implemented
    };
  }
}
