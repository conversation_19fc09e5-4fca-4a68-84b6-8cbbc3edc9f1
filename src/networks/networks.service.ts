import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Network } from '../networks/entities/network.entity';
import { Logger } from '@nestjs/common';
import { NetworkHelperService } from './network.helper.service';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class NetworksService {
  private readonly logger = new Logger(NetworksService.name);
  
  constructor(
    @InjectEntityManager() 
    private entityManager: EntityManager, 
    private networkHelperService: NetworkHelperService,
    private readonly httpService: HttpService
  ) {}

  // Get all networks
  async findAll() {
    console.log("Finding all networks");
    const networks = await this.entityManager
      .createQueryBuilder(Network, 'network') 
      .orderBy('network.name', 'ASC') 
      .getMany(); 
    
    return networks;
  }

  // Get all active networks
  async findAllActive() {
    console.log("Getting all active networks");
    const networks = await this.entityManager
      .createQueryBuilder(Network, 'network') 
      .where('network.is_active = :is_active', { is_active: true })
      .orderBy('network.name', 'ASC') 
      .getMany(); 
    
    return networks;
  }

  // Get grouped networks
  async findGrouped(): Promise<any> {
    console.log("Grouping networks");
    const networks = await this.findAllActive();
    const grouped = {};
    console.log(networks);
    
    networks.forEach(network => {
      // Skip networks without display_name
      if (!network.display_name) {
        return;
      }

      const baseName = network.display_name.split('-')[0]; // ethereum, polygon, arbitrum
      
      if (!grouped[baseName]) {
        grouped[baseName] = {
          name: baseName,
          mainnet: null,
          testnets: []
        };
      }
      
      if (network.is_testnet) {
        grouped[baseName].testnets.push(network);
      } else {
        grouped[baseName].mainnet = network;
      }
    });
    console.log(grouped);
    return Object.values(grouped);
  }

  // Find mainnets
  async findMainnets(): Promise<Network[]> {
    return this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.is_active = :is_active', { is_active: true })
      .andWhere('network.is_testnet = :is_testnet', { is_testnet: false })
      .orderBy('network.name', 'ASC')
      .getMany();
  }

  // Find testnets
  async findTestnets(): Promise<Network[]> {
    return this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.is_active = :is_active', { is_active: true })
      .andWhere('network.is_testnet = :is_testnet', { is_testnet: true })
      .orderBy('network.name', 'ASC')
      .getMany();
  }

  // Search via ID
  async findOneWithId(id: number): Promise<Network> {
    const network = await this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.id = :id', { id })
      .getOne();
    
    if (!network) {
      throw new NotFoundException(`Network with ID ${id} not found`);
    }
    
    return network;
  }

  // Search via name
  async findOneWithName(display_name: string): Promise<Network> {
    const network = await this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.display_name = :display_name', { display_name })
      .andWhere('network.is_active = true')
      .getOne();


    // delete this chnage to enum 
    if (!network) {
    const networksList = await this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.is_active = true')
      .select(['network.display_name'])
      .getMany();
      const networkList = await networksList.map(n => n.display_name).join(', ');
      throw new BadRequestException(`Network '${display_name}' not found. Available networks: ${networkList}`);
    }
    return network
  }

  // Create new network
  async create(createNetworkDto: CreateNetworkDto): Promise<String> {
    try{
      const network = this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.name = :name', { name: createNetworkDto.name })
      .orWhere('network.display_name = :display_name', { display_name: createNetworkDto.display_name })
      .getOne();
    
      if (!network) {
        throw new BadRequestException('Network already exists');
      }
    }catch(error){
      throw new BadRequestException("Failed to create network",error.message);
    }
    await this.entityManager.save(Network, createNetworkDto);
    return "Network created successfully";
  }

  // Edit network info
  async update(id: number, updateNetworkDto: UpdateNetworkDto): Promise<Network> {
    const network = await this.findOneWithId(id);

    // Filter out null and undefined values to prevent overwriting with nulls
    const filteredDto = Object.fromEntries(
      Object.entries(updateNetworkDto).filter(([_, value]) => value !== null && value !== undefined)
    );

    Object.assign(network, filteredDto);
    return this.entityManager.save(Network, network);
  }

  // Delete network
  async remove(id: number): Promise<void> {
    const result = await this.entityManager
      .createQueryBuilder()
      .softDelete()
      .from(Network)
      .where('id = :id', { id })
      .execute();
    
    if (result.affected === 0) {
      throw new NotFoundException(`Network with ID ${id} not found`);
    }
  }

  async pause(id: number): Promise<Network> {
    await this.findOneWithId(id);
    
    await this.entityManager
      .createQueryBuilder()
      .update(Network)
      .set({ is_active: false })
      .where('id = :id', { id })
      .execute();
    
    return this.findOneWithId(id);
  }

  // Activate network
  async activate(id: number): Promise<Network> {
    await this.findOneWithId(id);

    await this.entityManager
      .createQueryBuilder()
      .update(Network)
      .set({ is_active: true })
      .where('id = :id', { id })
      .execute();

    return this.findOneWithId(id);
  }


  async validateNetwork(network_name: string): Promise<boolean> {
    const network = await this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.display_name = :network_name AND network.is_active = true', { network_name })
      .getOne();

    if (!network) {
      // Get all available networks for error message
      const availableNetworks = await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.is_active = true')
        .select(['network.display_name'])
        .getMany();

      const networkList = availableNetworks.map(n => n.display_name).join(', ');
      throw new BadRequestException(`Network '${network_name}' not found. Available networks: ${networkList}`);
    }
    return true;
  }

  async getLatestBlockNumber(network_name: string): Promise<number> {
    try {
      const network = await this.findOneWithName(network_name);

      // Use the blockchain helper based on network type 
      const blockNumberHex = await this.networkHelperService.getBlockNumber(network.display_name, network.type);
      return await this.processBlockNumber(blockNumberHex);
      
    } catch (error) {
      console.error(`Failed to get latest block number for ${network_name}:`, error.message);
      throw new BadRequestException(error.message);
    }
  }

  // blocknumber ( 0x1234 ) -> sequence (27364)
  async processBlockNumber(blockNumberHex: string): Promise<number> {
    const blockNumberDecimal = parseInt(blockNumberHex, 16);

    if (isNaN(blockNumberDecimal)) {
      throw new Error(`Invalid hex block number: ${blockNumberHex}`);
    }

    return blockNumberDecimal;
  }


  async testingGetBlocknumber(): Promise<any> {
    const url = "https://weathered-young-aura.ethereum-sepolia.quiknode.pro/1ecc5b9d230d6b74ec0b4e0104fab1a6759b1ac5"
    const payload = {"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}
    const response = await firstValueFrom(
      this.httpService.post(
        url,
        payload,
        { headers: { 'Content-Type': 'application/json' } }
      )
    );
    return response.data;
  }
}

