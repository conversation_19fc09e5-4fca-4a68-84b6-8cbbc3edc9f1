import { Transform, Type } from "class-transformer";
import { IsInt, IsOptional, <PERSON>, <PERSON> } from "class-validator";
import { ApiPropertyOptional } from "@nestjs/swagger";

export class GetAllQnWebhookPaginationDto {

  @ApiPropertyOptional({
    description: 'Number of webhooks to return',
    minimum: 1,
    maximum: 100,
    example: 20
  })
  @Min(1)
  @Max(100)
  @IsInt()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of webhooks to skip',
    minimum: 0,
    example: 0
  })
  @Min(0)
  @IsInt()
  @IsOptional()
  offset?: number;
}
