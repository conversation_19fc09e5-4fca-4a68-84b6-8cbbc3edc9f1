import { <PERSON><PERSON><PERSON>y, IsEmail, Is<PERSON>num, IsEthereumAddress, IsString, IsUrl, ValidateNested } from "class-validator";
import { Type } from "class-transformer";
import { Optional } from "@nestjs/common";
import { ApiProperty } from "@nestjs/swagger";

export enum NetworkType {
  ETHEREUM = 'ethereum-mainnet',
  POLYGON = 'polygon-mainnet', 
  ARBITRUM = 'arbitrum-mainnet',
  BITCOIN = 'bitcoin-mainnet',
}

export enum QuickNodeTemplate {
  EVM_WALLET_FILTER = 'evmWalletFilter',
  EVM_CONTRACT_EVENTS = 'evmContractEvents',
  SOLANA_WALLET_FILTER = 'solanaWalletFilter',
  BITCOIN_WALLET_FILTER = 'bitcoinWalletFilter',
  EVM_ABI_FILTER = 'evmAbiFilter',
}

// can we do partial type 

export class TemplateArgsDto {

  @ApiProperty({ example: ['******************************************'] })
  @IsArray()
  @Optional()
  @IsEthereumAddress({ each: true })    //for now caters to only EVM addresses
  wallets: string[] = [];
}

export class CreateQnWebhookTemplateDto {
  @ApiProperty({ example: 'Template Name Here Now'})
  @IsString()
  name: string;

  @ApiProperty({ example: 'ethereum-mainnet'})
  @IsEnum(NetworkType)
  network: NetworkType;

  @ApiProperty({ example: '<EMAIL>'})
  @IsEmail()
  notification_email: string;

  @ApiProperty({ example: 'https://webhook.site/bff9438c-8c87-4b71-9a2c-dfe553fec52' })
  @IsUrl()
  destination_url: string;

  get destination_attributes() {
    return {
      url: this.destination_url,
      compression: 'none'
    };
  }

  @ApiProperty({ example: 'evmWalletFilter'})
  @IsEnum(QuickNodeTemplate)
  template: QuickNodeTemplate;

  @ValidateNested()
  @Optional()
  @Type(() => TemplateArgsDto)
  templateArgs: TemplateArgsDto;

}