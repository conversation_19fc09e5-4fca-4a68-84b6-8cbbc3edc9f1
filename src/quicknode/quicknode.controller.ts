import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQnWebhookDto } from './dto/create-qn-webhook.dto';
import { CreateQnWebhookTemplateDto } from './dto/create-qn-webhook-template.dto';
import { GetAllQnWebhookPaginationDto } from './dto/getAll-qn-webhook-pagination.dto';
import { UpdateQnWebhookDto } from './dto/update-qn-webhook.dto';
import { FilterQnWebhookDto } from './dto/filter-qn-webhook.dto';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQnWebhookDto) {
    console.log("Creating Quicknode webhook");
    return await this.quicknodeService.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  // create webhook with template ( filters, contract , wallet )
  @Post('create/webhook/template/evmWalletFilter')
  async createWebhookWithTemplate(@Body() dto: CreateQnWebhookTemplateDto) {
    console.log("Creating Quicknode webhook with template");
    const wallets = dto.templateArgs?.wallets || [];
    console.log("These are the existing wallets to filter\n", wallets);
    return await this.quicknodeService.createWebhookWithTemplate(
      dto.name,
      dto.network,
      dto.notification_email,
      dto.destination_url,
      dto.template,
      wallets
    );
  }

  // get all webhooks
  @Get("retrieve/webhooks")
  async getAllWebhooks(@Query() dto: GetAllQnWebhookPaginationDto) {
    console.log("Getting all webhooks");
    return await this.quicknodeService.getAllWebhooks(dto.limit, dto.offset);
  }

  // get webhook by id 
  @Get("retrieve/webhook/:id")
  async getWebhookById(@Query('id') id: string ) {
    console.log("Getting webhook by id", id);
    return await this.quicknodeService.getWebhookById(id);
  }

  // update existing webhook 
  @Patch("update/webhook/:id")
  async updateWebhook(@Param('id') id: string, @Body() dto: UpdateQnWebhookDto) {
    console.log("Updating webhook by id", id);
    return await this.quicknodeService.updateWebhook(id, dto);
  }

  // delete webhook by id 
  @Delete("delete/webhook/:id")
  async deleteWebhook(@Param('id') id: string) {
    console.log("Deleting webhook by id", id);
    return await this.quicknodeService.deleteWebhook(id);
  }

  // activate webhook by id 
  @Post("activate/webhook/:id")
  async activateWebhook(@Param('id') id: string, @Body('startFrom') startFrom: string = 'latest'){
    console.log("Activating webhook by id", id);
    return await this.quicknodeService.activateWebhook(id, startFrom),"Webhook Activated";
  }

  //pause webhook by id 
  @Post("paused/webhook/:id")
  async pausedWebhook(@Param('id') id: string){
    console.log("Pausing webhook by id", id);
    return await this.quicknodeService.pauseWebhook(id),"Webhook Paused";
  }

  //test filter 
  @Post("filter/webhook/")
  async testFilter(@Body() dto: FilterQnWebhookDto){
    console.log("testing filter ")
    return await this.quicknodeService.testFilter(dto);
  }


}
