import { Test, TestingModule } from '@nestjs/testing';
import { QuicknodeController } from './quicknode.controller';
import { QuicknodeService } from './quicknode.service';

describe('QuicknodeController', () => {
  let controller: QuicknodeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QuicknodeController],
      providers: [QuicknodeService],
    }).compile();

    controller = module.get<QuicknodeController>(QuicknodeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
