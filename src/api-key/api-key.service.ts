import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { ApiKey } from './entities/api-key.entity';
import * as crypto from 'crypto';
import { User } from 'src/users/entities/user.entity';
import * as bcrypt from 'bcrypt';


@Injectable()
export class ApiKeyService {
  // only create and regenerate can return secret key
  // other functions should never return secret key
  private readonly MAX_API_KEYS_PER_USER = 3; 

  constructor(
     @InjectEntityManager()
    private entityManager: EntityManager,
  ){}

  async generateApiKey(): Promise<string> {
    const prefix = 'wAlt-';
    const randomPart = crypto.randomBytes(16).toString('hex');
    return await `${prefix}${randomPart}`;
  }

  async generateSecretKey(): Promise<string> {
    return await crypto.randomBytes(32).toString('hex');
  }

  async checkIfKeyExists(id: number): Promise<boolean> {
    if (!id || id <= 0) {
      throw new BadRequestException('Invalid API key ID');
    }
    const apiKey = await this.entityManager
      .createQueryBuilder(ApiKey, 'api_key')
      .where('api_key.id = :id', { id })
      .getOne();  
    
    if (!apiKey) {
      throw new BadRequestException(`API key with ID ${id} not found`);
    }
    return true;
  }

  async create(createApiKeyDto: CreateApiKeyDto): Promise<ApiKey & { secret_key: string }>{
    try{
      // if user exists
      const userExists = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.id = :id', { id: createApiKeyDto.user_id })
        .getOne();
      if (!userExists) {
        throw new BadRequestException('User does not exist');
      }

      // if reach max api key count
      try {
        const existingKeysCount = await this.entityManager
        .createQueryBuilder(ApiKey, 'api_key')
        .where('api_key.user_id = :user_id', { user_id: createApiKeyDto.user_id })
        .getCount();
        if (existingKeysCount >= this.MAX_API_KEYS_PER_USER) {
        throw new BadRequestException(`User can only have a maximum of ${this.MAX_API_KEYS_PER_USER} API keys`);
      }
      }catch(error){
        throw new BadRequestException('API key limit reached');
      }

      // if key name exists 
      const existingKeyName = await this.entityManager
        .createQueryBuilder(ApiKey, 'api_key')
        .where('api_key.user_id = :userId AND api_key.key_name = :keyName', {
          userId: createApiKeyDto.user_id,
          keyName: createApiKeyDto.key_name,
        })
        .getOne();

      if (existingKeyName) {
        throw new BadRequestException('API key name already exists for this user');
      }

      // set params
      const now = new Date();
      const expiresAt = createApiKeyDto.expires_at
        ? new Date(createApiKeyDto.expires_at)
        : new Date(now.setFullYear(now.getFullYear() + 3));

      const apiKey = await this.generateApiKey();
      const secretKey = await this.generateSecretKey();
      const hashedSecret = await bcrypt.hash(secretKey, 10);

      const insertNewKey = await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(ApiKey)
        .values({
          user_id: createApiKeyDto.user_id,
          key_name: createApiKeyDto.key_name,
          api_key: apiKey,
          secret_key: hashedSecret,
          permissions_id: createApiKeyDto.permissions_id, 
          is_active: true,
          expires_at: expiresAt, 
        })
        .returning('*')
        .execute();

        const savedApiKey = insertNewKey.generatedMaps[0] as ApiKey;

        return {
          ...savedApiKey,
          secret_key: secretKey,
        };
    }catch(error){
      throw new BadRequestException("Failed to create API key",error.message);
    }
  }

  async getAllUserKeys(userId: number){
    // if user exists
    const userExists = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: userId })
      .getOne();
    if (!userExists) {
      throw new BadRequestException('User does not exist');
    }
    // dont take secret key
    return await this.entityManager
      .createQueryBuilder(ApiKey, 'api_key')
      .where('api_key.user_id = :userId', { userId })
      .select(['api_key.id', 'api_key.api_key', 'api_key.key_name','api_key.permissions_id', 'api_key.is_active', 'api_key.expires_at'])
      .getMany();
  }

  async remove(id:number){
    await this.checkIfKeyExists(id);
    await this.entityManager
      .createQueryBuilder()
      .softDelete()
      .from(ApiKey)
      .where('id = :id', { id })
      .execute();

    return { message: 'API key deleted successfully' };
  }

   async update(id: number, updateApiKeyDto: UpdateApiKeyDto) {
    await this.checkIfKeyExists(id);

    // Filter out undefined/null values to only update provided fields
    const updateFields = {};
    
    if (updateApiKeyDto.key_name !== undefined) {
      updateFields['key_name'] = updateApiKeyDto.key_name;
    }
    
    if (updateApiKeyDto.is_active !== undefined) {
      updateFields['is_active'] = updateApiKeyDto.is_active;
    }
    
    if (updateApiKeyDto.expires_at !== undefined) {
      updateFields['expires_at'] = updateApiKeyDto.expires_at;
    }

    // Only proceed if there are fields to update
    if (Object.keys(updateFields).length === 0) {
      throw new BadRequestException('No fields provided to update');
    }

    // Perform the update with only provided fields
    await this.entityManager
      .createQueryBuilder()
      .update(ApiKey)
      .set(updateFields)
      .where('id = :id', { id })
      .execute();

    // Get updated record
    const updatedApiKey = await this.entityManager
      .createQueryBuilder(ApiKey, 'api_key')
      .where('api_key.id = :id', { id })
      .select([
        'api_key.id', 
        'api_key.api_key', 
        'api_key.key_name', 
        'api_key.is_active', 
        'api_key.expires_at',
        'api_key.created_at',
        'api_key.updated_at'
      ])
      .getOne();

    return updatedApiKey;
  }

  async regenerateNewSecret(id: number): Promise<{ secret_key: string }> {
    await this.checkIfKeyExists(id);

    try {
      const newSecretKey = await this.generateSecretKey();
      const hashedSecret = await bcrypt.hash(newSecretKey, 10);

      // Update secret key
      await this.entityManager
        .createQueryBuilder()
        .update(ApiKey)
        .set({ secret_key: hashedSecret })
        .where('id = :id', { id })
        .execute();

      return { secret_key: newSecretKey };
    } catch (error) {
      throw new BadRequestException('Failed to regenerate secret key');
    }
  }

  async validateApiKey(apiKey: string): Promise<ApiKey> {
    const key = await this.entityManager
      .createQueryBuilder(ApiKey, 'api_key')
      .where('api_key.api_key = :apiKey AND api_key.is_active = :isActive', {
        apiKey,
        isActive: true,
      })
      .getOne();

    if (!key) {
      throw new BadRequestException('Invalid API key');
    }
    // Check if expired
    if (key.expires_at && new Date() > key.expires_at) {
      throw new BadRequestException('API key has expired');
    }

    return key;
  }

  async getUserFromApiKey(apiKey: string): Promise<User> {
    const key = await this.validateApiKey(apiKey);

    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :userId', { userId: key.user_id })
      .getOne();

    if (!user) {
      throw new BadRequestException('User not found');
    }

    return user;
  }

}
