import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { ApiKeyService } from './api-key.service';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';
import { ApiQuery } from '@nestjs/swagger';

@Controller('api-key')
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

    // POST /api-keys → Create new API key
    @Post('create')
    async create(@Body() createApiDto: CreateApiKeyDto) {
      console.log("creating API key")
      return await this.apiKeyService.create(createApiDto);
    }

    // GET /api-keys → List all API keys for current user
    @Get('getAllKeys')
    async getAllUserKeys(@Query('userId') userId: number ) {
      console.log("getting all existing apikeys")
      return await this.apiKeyService.getAllUserKeys(userId);
    }

    // DELETE /api-keys/:id → Delete API key (soft delete)
    @Get('remove')
    async remove(@Query('id') id: number) {
      console.log("deleting api key")
      return await this.apiKeyService.remove(id);
    }

    // PATCH /api-keys/:id → Update key name, permissions, or status
    @Post('update')
    async update(@Query('id') id: number, @Body() updateData: UpdateApiKeyDto ){
      console.log("updating api key")
      return await this.apiKeyService.update(id, updateData);
    }

    // PATCH /api-keys/regenerateSecret/:id → delete old secret generate and return new one
    @Get('regenerateNewSecret')
    async regenerateNewSecret(@Query('id') id: number ){
      return await this.apiKeyService.regenerateNewSecret(id);
    }

    // GET /api-keys/validate → Validate API key and return user info
    @Get('validate')
    async validateApiKey(@Query('apiKey') apiKey: string ){
      return await this.apiKeyService.validateApiKey(apiKey);
    }

    // GET /api-keys/user → Get user info from API key
    @Get('getUser')
    async getUserFromApiKey(@Query('apiKey') apiKey: string ){
      return await this.apiKeyService.getUserFromApiKey(apiKey);
    }
}