import { ApiProperty } from "@nestjs/swagger";
import { IsA<PERSON>y, IsBoolean, IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, Matches } from "class-validator";
import { PrimaryGeneratedColumn } from "typeorm";


export class CreateApiKeyDto {
  @ApiProperty({ example: 'My second API Key'})
  @IsNotEmpty()
  @IsString()
  key_name: string;

  @ApiProperty({ example: 1})
  @IsNotEmpty()
  @IsInt()
  user_id: number;

  @ApiProperty({ example: 1})
  @IsNotEmpty()
  @IsInt()
  permissions_id: number;

  @ApiProperty({ example: true})
  @IsNotEmpty()
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({ example: '' })
  @IsNotEmpty()
  @IsDateString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'expires_at must be in YYYY-MM-DD format' })
  expires_at: string;
}