import { ApiProperty } from "@nestjs/swagger";
import { IsA<PERSON>y, IsBoolean, IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, Matches } from "class-validator";
import { Column, PrimaryGeneratedColumn } from "typeorm";

export class UpdateApiKeyDto{
  @ApiProperty({ example: 'My second API Key'})
  @IsNotEmpty()
  @IsString()
  key_name: string;

  @ApiProperty({ example: true})
  @IsOptional()
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({ example: '' })
  @IsOptional()
  @IsDateString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'expires_at must be in YYYY-MM-DD format' })
  expires_at: string;
}
