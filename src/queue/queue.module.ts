import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TransferQueueService } from './transfer-queue.service';
@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([]),
    BullModule.registerQueue(
      {
        name: 'transfer',
        redis: {
          port: 6379,
        },
      },
    ),
  ],
  providers: [
    TransferQueueService
  ],
})
export class QueueModule {}
