import { OnQueue<PERSON><PERSON>, Process, Processor } from "@nestjs/bull";
import { Job } from "bull";
import { <PERSON><PERSON><PERSON> } from "src/moralis/entities/morali.entity";
import { EntityManager, Equal } from "typeorm";

@Processor('transfer')
export class TransferQueueService {
  constructor(
    private em: EntityManager,
  ) {}

  @Process('process-transfer')
  async readOperationJob3(job: Job) {
    let data = job.data;
    switch (data.type) {
      case 'token-transfer':
        await this.processTokenTransfer(data.transactions);
        break;
      default:
        break;
    }
  }

  private async processTokenTransfer(transactions) {
    try {
      for(const formatTransaction of transactions) {
        if (formatTransaction.type === 'token-transfer') {
          const trxHash = formatTransaction.transactionHash;
          const token_addr = formatTransaction.contract;
          const amount = formatTransaction.value;
          const decodedAmount = formatTransaction.valueWithDecimals;

          let txn = await this.em.findOneBy(Morali, {
            hash: Equal(trxHash)
          });

          if(!txn) {
            txn = new Morali();
            txn.amount = amount;
            txn.hash = trxHash;
            await this.em.save(Morali, txn);
          }
          
          continue;
        }
        
        if (formatTransaction.type === 'native-transfer') {
          const trxHash = formatTransaction.transactionHash;
          const amount = formatTransaction.value;

          let txn = await this.em.findOneBy(Morali, {
            hash: Equal(trxHash)
          });

          if(!txn) {
            txn = new Morali();
            txn.amount = amount;
            txn.hash = trxHash;
            await this.em.save(Morali, txn);
          }
          
          continue;
        }
      }
    } catch (error) {
      console.log(
        `-----------------------------------------------------------------------`,
      );
      console.log(new Date());
      console.log(error, 'in transfer queue');
      console.log(
        `-----------------------------------------------------------------------`,
      );
    } 
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name} with data ${job.data}...`,
    )
  }
}