import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from "class-validator";

enum WebhookStatus {
  ACTIVE = 'active',
  PAUSED = 'paused', 
  TERMINATED = 'terminated',
  RESUMING = 'resuming'
}

class DestinationAttributesDto {
  @ApiProperty({ example: "https://scanner.cake5x.com/transaction-router/receive/1" })
  @IsNotEmpty()
  @IsString()
  url: string;
}

export class CreateWebhookDto {
 @ApiProperty({ example: "banana rama"})
  @IsNotEmpty()
  @IsString()
  webhook_name: string;

  @ApiProperty({ example: "ethereum-mainnet"})
  @IsNotEmpty()
  @IsString()
  network_name: string;

  @ApiProperty({
    example: {
      url: "https://scanner.cake5x.com/transaction-router/receive/1",
      compression: "none"
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DestinationAttributesDto)
  destination_attributes: DestinationAttributesDto;


  @ApiProperty({ example: "active", enum: ["active", "paused","terminated","resuming"] })
  @IsEnum(["active", "paused","terminated","resuming"])
  @IsNotEmpty()
  status: string;

  @ApiProperty({ example: ["0xabc123...","0xdef456..."] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  wallets?: string[] = []; // wallet addresses to monitor

  @ApiProperty({ example: ["0xabc123...","0xdef456..."] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  contracts?: string[] = []; // contract addresses to monitor
}