import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { ApiSecurity } from '@nestjs/swagger';
import { ApiKeyAuthGuard } from 'src/auth/api-key.guard';

@Controller('webhooks')
@UseGuards(ApiKeyAuthGuard)
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('create')
  @ApiSecurity('x-api-key')
  async create(@Body() createWebhookDto: CreateWebhookDto , @Request() req) {
    return this.webhooksService.createWebhook(createWebhookDto, req.user.id);
  }


  // get all webhooks for user using apikey to find user id
  // have params like ?limit=20&offset=0  
  @Get('getAll')
  @ApiSecurity('x-api-key')
  async getAllWebhooks(@Request() req) {
    console.log("Getting all webhooks for user", req.user.id);
    return this.webhooksService.getAllWebhooks(req.user.id);
  }




}
