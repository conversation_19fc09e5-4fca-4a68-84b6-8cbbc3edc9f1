import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Webhook } from './entities/webhook.entity';
import { User } from 'src/users/entities/user.entity';
import { NetworksService } from 'src/networks/networks.service';
import { ApiKeyService } from 'src/api-key/api-key.service';
import { Network } from 'src/networks/entities/network.entity';

@Injectable()
export class WebhooksService {
  constructor( 
    @InjectEntityManager()
    private entityManager : EntityManager,
    private networksService: NetworksService,
    private apiKeyService: ApiKeyService,
  ){}

  async generateSecurityToken(): Promise<string> {
    const prefix = 'wsLAF_';
    const token = crypto.randomBytes(32).toString('base64');
    return `${prefix}${token}`;
  }

  async checkIfWebhookExists(id: string): Promise<boolean> {
    if (!id) {
      throw new BadRequestException('Invalid webhook ID');
    }
    
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .getOne();
    
    if (!webhook) {
      throw new BadRequestException(`Webhook with ID ${id} not found`);
    }
    return true;
  }

  async validateWebhookOwnership(user_id: number, webhook_name: string){
      const validateWebhookName = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('webhook.user_id = :user_id AND webhook.webhook_name = :webhook_name', {
          user_id,
          webhook_name,
        })
        .getOne();

      if (validateWebhookName) {
        throw new BadRequestException('Webhook name already exists for this user');
      }
  }

  async cleanMonitorAddresses(addresses: { wallets?: string[], contracts?: string[] }) {
    const result: any = {};
    if (addresses.wallets && addresses.wallets.length > 0) {
      result.wallets = addresses.wallets;
    }
    if (addresses.contracts && addresses.contracts.length > 0) {
      result.contracts = addresses.contracts;
    }
    return Object.keys(result).length > 0 ? result : { wallets: [] }; // default if both empty
  }


  async createWebhook(createWebhookDto: CreateWebhookDto , user_id: number): Promise<any> {
    // use auth to check for user 
    try {
      // check if network is valid
      await this.networksService.validateNetwork(createWebhookDto.network_name);

      // check if user has a webhook with this name
      await this.validateWebhookOwnership(user_id, createWebhookDto.webhook_name);

      // might wanna delete this ( redundant )
      const network =await this.networksService.findOneWithName(createWebhookDto.network_name);
      console.log(network)
      const currentBlockNumber = await this.networksService.getLatestBlockNumber(createWebhookDto.network_name);

      // get latest block number from network and set start position to that block number
      const securityToken = await this.generateSecurityToken();

      const destinationAttributes = {
        url: createWebhookDto.destination_attributes.url,
        security_token: securityToken,
      };

      const monitorAddresses = await this.cleanMonitorAddresses({
        wallets: createWebhookDto.wallets || [],
        contracts: createWebhookDto.contracts || []
      });

      const user = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.id = :user_id', { user_id: user_id })
        .getOne();

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const insertResult = await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Webhook)
        .values({
          user_id: user_id,
          network_id: network.id,
          webhook_name: createWebhookDto.webhook_name,
          webhook_url: createWebhookDto.destination_attributes.url,
          security_tokens: securityToken,
          start_position: currentBlockNumber,
          status: createWebhookDto.status as any, // Cast to enum type
          wallet_transactions: createWebhookDto.wallets || [], // Save wallet addresses
          contracts: createWebhookDto.contracts || [], // Save contract addresses
        })
        .returning('*')
        .execute();

        const savedWebhook = insertResult.generatedMaps[0] as Webhook;

        return {
        id: savedWebhook.id,
        name: savedWebhook.webhook_name,
        status: savedWebhook.status,
        created_at: savedWebhook.created_at,
        destination_attributes: destinationAttributes,
        network: createWebhookDto.network_name,
        notification_email: user.email,
        sequence: currentBlockNumber,
        updated_at: savedWebhook.updated_at,
        monitorAddresses: monitorAddresses
      };
    } catch (error) {
      throw new BadRequestException("Failed to create webhook", error.message);
    }
  }

  async getAllWebhooks(user_id: number): Promise<any[]> {
   const webhooks = await this.entityManager
    .createQueryBuilder(Webhook, 'webhook')
    .leftJoin(Network, 'network', 'network.id = webhook.network_id')
    .where('webhook.user_id = :user_id AND webhook.deleted_at IS NULL', { user_id })
    .orderBy('webhook.created_at', 'DESC')
    .select([
      'webhook.id',
      'webhook.url',
      'webhook.created_at',
      'network.display_name',
    ])
    .getRawMany();

    
    return webhooks.map((webhook) => ({
      id: webhook.id,
      name: webhook.webhook_name,
      status: webhook.status,
      created_at: webhook.created_at,
      updated_at: webhook.updated_at,
      destination_attributes: {
        url: webhook.webhook_url,
        security_token: webhook.security_tokens,
      },
      network: webhook.network_name,
      notification_email: user.email,
      sequence: webhook.start_position,
      monitorAddresses: await this.cleanMonitorAddresses({
        wallets: webhook.wallet_transactions || [],
        contracts: webhook.contracts || [],
      }),
    }));
  }
}

