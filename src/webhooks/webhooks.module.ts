import { Module } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { WebhooksController } from './webhooks.controller';
import { NetworksModule } from 'src/networks/networks.module';
import { ApiKeyModule } from 'src/api-key/api-key.module';

@Module({
  controllers: [WebhooksController],
  providers: [WebhooksService],
  imports: [NetworksModule, ApiKeyModule]
})
export class WebhooksModule {}
