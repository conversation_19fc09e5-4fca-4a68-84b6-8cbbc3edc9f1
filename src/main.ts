import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import configuration from 'config/configuration';
import { JsendObjectError, JsendObjectSuccess } from 'common/interface/jsend-interface';
import { PageDto } from 'common/dto/pageResponse.dto';
import { PageOptionsDto } from 'common/dto/pagination.dto';
import * as dotenv from 'dotenv';
import * as express from 'express';
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ limit: '10mb', extended: true }));

  const config = new DocumentBuilder()
    .setTitle('Scanner API')
    .setVersion('1.0')
    .addBearerAuth()
    .addApiKey(
      {
        type: 'api<PERSON><PERSON>',
        name: 'x-api-key', // this is your header key
        in: 'header',
      },
      'x-api-key', // must match the @ApiSecurity() decorator in your controller
    )
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [
      JsendObjectSuccess,
      JsendObjectError,
      PageDto,
      PageOptionsDto,
    ],
  });
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: { defaultModelsExpandDepth: -1 },
  });

  await app.listen(configuration().port, '0.0.0.0');
}
bootstrap();
